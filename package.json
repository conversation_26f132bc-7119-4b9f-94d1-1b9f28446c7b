{"name": "solace-generic-electron-consumer", "version": "1.0.0", "description": "Electron app for consuming messages from Solace topics", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "solace", "messaging", "consumer"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"electron": "^36.4.0", "electron-builder": "^26.0.12"}, "dependencies": {"solclientjs": "^10.18.0", "dotenv": "^16.5.0"}, "overrides": {"glob": "^11.0.0", "rimraf": "^6.0.1", "inflight": "npm:@isaacs/inflight@^1.0.6", "@npmcli/move-file": "npm:@npmcli/fs@^4.0.0", "boolean": "^3.2.0"}, "build": {"appId": "com.solace.electron.consumer", "productName": "Solace Message Consumer", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/solclientjs/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}